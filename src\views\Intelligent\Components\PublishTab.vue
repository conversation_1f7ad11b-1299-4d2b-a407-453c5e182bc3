<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { copyText } from '@/utils/common';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { PlusOutlined, MinusCircleOutlined, FileTextOutlined, EditOutlined } from '@ant-design/icons-vue';
import type { ColumnType } from 'ant-design-vue/es/table';
import EmbedDetailModal from './EmbedDetailModal.vue';
import type { IAgentItem } from '@/interface/agent';
import dayjs from 'dayjs';
import { publishAgent, publishChannel } from '@/api/agent';
import { useRoute } from 'vue-router';

// 定义 props
interface Props {
  agentId: string;
  agentDetail?: IAgentItem;
}
const props = defineProps<Props>();

// 发布渠道数据接口
interface PublishChannel {
  key: string;
  channel: string;
  channelIcon: string;
  description: string;
  status: 'published' | 'unpublished';
  publishTime?: string;
  actions: string[];
}

// 已配置网站数据接口
interface ConfiguredWebsite {
  id: string;
  name: string;
  domainCount: number;
  status: 'active' | 'inactive';
}

// 表格数据
const dataSource = ref<PublishChannel[]>([
  {
    key: '1',
    channel: '网页版',
    channelIcon: 'globe',
    description: '可通过 PC 端浏览器访问的网页版本对话',
    status: 'published',
    publishTime: '2025.07.30 11:36:24',
    actions: ['visit', 'copy']
  },
  {
    key: '2',
    channel: '网站嵌入',
    channelIcon: 'code',
    description: '通过 iframe/JS 嵌入到其他网站或应用的嵌入式体验版本',
    status: 'unpublished',
    actions: ['website']
  }
]);

// 已配置网站数据
const configuredWebsites = ref<ConfiguredWebsite[]>([
  {
    id: '1',
    name: '环球数科',
    domainCount: 1,
    status: 'active'
  },
  {
    id: '2',
    name: '客服系统',
    domainCount: 3,
    status: 'active'
  }
]);

// 表格列配置
const columns: ColumnType[] = [
  {
    title: '发布渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 200,
  },
  {
    title: '发布结果',
    dataIndex: 'status',
    key: 'status',
    width: 200,
  },
  {
    title: '操作',
    key: 'actions',
    width: 300,
  },
];

// 弹窗状态
const isModalVisible = ref(false);
const isEmbedDetailVisible = ref(false);
const selectedWebsite = ref<ConfiguredWebsite | null>(null);
const route = useRoute();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
interface FormData {
  websiteName: string;
  domains: string[];
}

const formData = reactive<FormData>({
  websiteName: '',
  domains: []
});

// 表单验证规则
const rules = {
  websiteName: [
    { required: true, message: '请输入网站名称', trigger: 'blur' },
    { max: 30, message: '网站名称不能超过 30 个字符', trigger: 'blur' }
  ]
};

// 操作处理函数
const handleVisit = () => {
  const href = `${location.origin}/intelligent/preview/${props.agentDetail?.master_id}`;
  window.open(href);
};

const handleCopyLink = () => {
    const href = `${location.origin}/intelligent/preview/${props.agentDetail?.master_id}`;
    copyText(href);
};

const publishConfig = computed(() => {
  const { publish_channel } = props.agentDetail;
  console.log(props.agentDetail)
  return {
    isWebPublish: publish_channel.includes('website'),
    publish_channel: ['website'],
    origins: formData.domains.map((domain) => ({
      website_name: formData.websiteName,
      domain
    }))
  };
});

watch(
    () => props.agentDetail,
    (newVal) => {
        console.log('newVal', newVal);
        if (newVal && newVal.updated_at) {
            const webChannel = dataSource.value.find((item) => item.key === '1');
            if (webChannel) {
                webChannel.publishTime = dayjs(newVal.updated_at).format('YYYY.MM.DD HH:mm:ss');
            }
        }
    },
    { immediate: true, deep: true },
);

const handleGoToWebsite = () => {
    isModalVisible.value = true;
};

// 弹窗相关函数
const handleModalOk = async () => {
  try {
    // 验证表单
    await formRef.value?.validate();
    // 这里应该提交表单数据
    console.log('提交表单数据：', formData);
    await publishChannel(String(route.params.id), {
          publish_channel: ['website', 'embedded'],
          origins: formData.domains.map((domain) => ({
            website_name: formData.websiteName,
            domain
          })),
    });
    message.success('网站添加成功');
    isModalVisible.value = false;
    resetForm();
  } catch (error) {
    console.log('表单验证失败：', error);
  }
};

const handleModalCancel = () => {
  isModalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  formData.websiteName = '';
  formData.domains = [];
  formRef.value?.resetFields();
};

// 域名输入框相关函数
const addDomain = () => {
  if (formData.domains.length < 10) {
    formData.domains.push('');
  }
};

const removeDomain = (index: number) => {
  formData.domains.splice(index, 1);
};

// 处理详情按钮点击
const handleShowDetail = (website: ConfiguredWebsite) => {
  selectedWebsite.value = website;
  isEmbedDetailVisible.value = true;
};

// 处理详情弹窗关闭
const handleEmbedDetailClose = () => {
  isEmbedDetailVisible.value = false;
  selectedWebsite.value = null;
};
</script>

<template>
  <div class="publish-tab-container">
    <div class="publish-header">
      <h3 class="publish-title">发布渠道及结果如下</h3>
    </div>

    <div class="publish-table-wrapper">
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :pagination="false"
        :bordered="true"
        size="middle"
      >
        <!-- 发布渠道列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'channel'">
            <div class="channel-cell">
              <div class="channel-icon">
                <img
                  v-if="record.channelIcon === 'globe'"
                  src="@/assets/image/base/pictures/web-icon.png"
                  alt="网页版"
                  class="channel-icon-img"
                />
                <img
                  v-else-if="record.channelIcon === 'code'"
                  src="@/assets/image/base/pictures/web-embedding.png"
                  alt="网站嵌入"
                  class="channel-icon-img"
                />
              </div>
              <div class="channel-info">
                <div class="channel-name">{{ record.channel }}</div>
                <div class="channel-desc">{{ record.description }}</div>
              </div>
            </div>
          </template>

          <!-- 发布结果列 -->
          <template v-else-if="column.dataIndex === 'status'">
            <div class="status-cell">
              <div v-if="record.status === 'published'" class="status-published">
                <div class="status-row">
                  <a-tag v-if="publishConfig.isWebPublish" color="success">已发布</a-tag>
                  <a-tag v-else color="warning">未发布</a-tag>
                </div>
                <div class="publish-time">{{ record.publishTime }}</div>
              </div>
              <div v-else class="status-unpublished">
                <a-tag v-if="configuredWebsites.length === 0" color="warning">未发布</a-tag>
                <a-tag v-else color="success">已发布</a-tag>
              </div>
            </div>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <div class="actions-cell">
              <template v-if="record.status === 'published'">
                <a-button
                type="link"
                  size="small"
                  class="action-btn"
                  @click="handleVisit"
                >
                  立即访问
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  class="action-btn"
                  @click="handleCopyLink"
                >
                  复制智能体链接
                </a-button>
              </template>
              <template v-else>
                <!-- 已配置网站列表 -->
                <div class="configured-websites">
                  <div v-for="website in configuredWebsites" :key="website.id" class="website-item">
                    <div class="website-info">
                      <div class="website-name">{{ website.name }}</div>
                      <div class="website-domain-count">已配置{{ website.domainCount }}个域名</div>
                    </div>
                    <div class="website-actions">
                      <a-button type="text" size="small" class="action-icon-btn" @click="handleShowDetail(website)">
                        <template #icon>
                          <FileTextOutlined />
                        </template>
                      </a-button>
                      <a-button type="text" size="small" class="action-icon-btn">
                        <template #icon>
                          <EditOutlined />
                        </template>
                      </a-button>
                      <a-button type="text" size="small" class="action-icon-btn danger">
                        <template #icon>
                          <MinusCircleOutlined />
                        </template>
                      </a-button>
                    </div>
                  </div>
                </div>

                <a-button
                  type="primary"
                  size="small"
                  @click="handleGoToWebsite"
                >
                  新增网站
                </a-button>
              </template>
            </div>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 新增网站弹窗 -->
    <a-modal
      v-model:open="isModalVisible"
      title="新增网站"
      :width="600"
      ok-text="完成并生成代码"
      cancel-text="取消"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <div class="modal-content">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
        >
          <!-- 网站名称 -->
          <a-form-item
            label="网站名称"
            name="websiteName"
            class="form-item"
          >
            <a-input
              v-model:value="formData.websiteName"
              placeholder="请输入网站名称"
              :maxlength="30"
              :show-count="true"
              class="form-input"
            />
          </a-form-item>

          <!-- 网站域名 -->
          <a-form-item
            label="网站域名"
            class="form-item"
          >
            <div class="domain-description">
              配置需要调用智能体的域名。为提升资源安全性，请按实际使用情况填写正确的域名。如不配置域名，则不限制请求来源
            </div>

            <!-- 域名输入框列表 -->
            <div class="domain-inputs">
              <div
                v-for="(_, index) in formData.domains"
                :key="index"
                class="domain-input-row"
              >
                <a-input
                  v-model:value="formData.domains[index]"
                  placeholder="请填写需要接入的网站域名，如：https://hqshuke.com/"
                  class="domain-input"
                />
                <MinusCircleOutlined
                  class="remove-domain-btn"
                  @click="removeDomain(index)"
                />
              </div>
            </div>

            <!-- 新增域名按钮 -->
            <div class="add-domain-btn-wrapper">
              <a-button
                v-if="formData.domains.length < 10"
                type="dashed"
                class="add-domain-btn"
                @click="addDomain"
              >
                <PlusOutlined />
                新增域名
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 嵌入详情弹窗 -->
    <EmbedDetailModal
      v-if="selectedWebsite"
      :visible="isEmbedDetailVisible"
      :website-info="{ name: selectedWebsite.name, domainCount: selectedWebsite.domainCount }"
      @update:visible="isEmbedDetailVisible = $event"
      @close="handleEmbedDetailClose"
    />
  </div>
</template>

<style scoped>
.publish-tab-container {
  padding: 20px;
  height: 100%;
  background: #fff;
}

.publish-header {
  margin-bottom: 20px;
}

.publish-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin: 0;
}

.publish-table-wrapper {
  background: #fff;
}

/* 发布渠道列样式 */
.channel-cell {
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.channel-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  /* width: 24px; */
  /* height: 24px; */
  margin-top: 2px;
}

.channel-icon-img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.channel-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 发布结果列样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-published {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-row {
  display: flex;
  align-items: center;
}

.status-unpublished {
  display: flex;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 操作列样式 */
.actions-cell {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.action-btn {
  padding: 0;
  height: auto;
  line-height: 1.4;
}

.action-btn + .action-btn {
  margin-top: 8px;
}

/* 已配置网站列表样式 */
.configured-websites {
  width: 100%;
  /* margin-bottom: 16px; */
}

.website-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

.website-item:last-child {
  margin-bottom: 0;
}

.website-info {
  display: flex;
  align-item: center;
  gap: 4px;
  flex: 1;
}

.website-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.website-domain-count {
  font-size: 12px;
  color: #8c8c8c;
}

.website-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-icon-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: color 0.3s;
}

.action-icon-btn:hover {
  color: #1890ff;
}

.action-icon-btn.danger:hover {
  color: #ff4d4f;
}

/* 表格样式调整 */
:deep(.ant-table) {
  border-radius: 6px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 16px;
  vertical-align: top;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 弹窗样式 */
.modal-content {
  padding: 20px 0;
}

.form-item {
  margin-bottom: 24px;
}

.form-input {
  width: 100%;
}

/* Ant Design 表单样式调整 */
:deep(.ant-form-item-label > label) {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

.domain-description {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.5;
  margin-bottom: 16px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
}

.domain-inputs {
  margin-bottom: 16px;
}

.domain-input-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.domain-input {
  flex: 1;
}

.remove-domain-btn {
  font-size: 16px;
  color: #ff4d4f;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s;
}

.remove-domain-btn:hover {
  color: #ff7875;
}

.add-domain-btn-wrapper {
  display: flex;
  justify-content: flex-start;
}

.add-domain-btn {
  height: 40px;
  border: 1px dashed #d9d9d9;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px;
}

.add-domain-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
